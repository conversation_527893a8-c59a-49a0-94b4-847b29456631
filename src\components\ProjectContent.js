"use client";

import { useModal } from '../contexts/ModalContext';

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  const { openModal } = useModal();

  const handleProjectClick = () => {
    openModal('project-details', project);
  };
  return (
    <div
      className="absolute inset-0 w-full h-full cursor-pointer"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
      }}
      onClick={handleProjectClick}
    >
      {/* Full background image with solid backing */}
      <div
        className="w-full h-full rounded-3xl overflow-hidden relative"
        style={{
          // Fade in effect: starts at 70% opacity, goes to 100% as mask progresses
          opacity: blurAmount > 0 ? 0.7 + (0.3 * (1 - blurAmount / 8)) : 1
        }}
      >
        {/* Solid background layer - uses primary theme color */}
        <div className="absolute inset-0 w-full h-full bg-primary"></div>

        {/* Image layer on top */}
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover relative z-10"
          />
        ) : (
          <div className="w-full h-full bg-primary/20 flex items-center justify-center relative z-10">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectContent;
