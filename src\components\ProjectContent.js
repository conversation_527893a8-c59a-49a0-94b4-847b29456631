"use client";

import { useModal } from '../contexts/ModalContext';

const ProjectContent = ({ project, opacity = 1, maskProgress = 0, blurAmount = 0, isCurrentProject = false, isNextProject = false }) => {
  const { openModal } = useModal();

  const handleProjectClick = () => {
    openModal('project-details', project);
  };

  // Calculate clip path based on project role
  let clipPath = 'inset(0 0 0 0)'; // Default: fully visible

  if (isNextProject) {
    // Next project: reveals from right to left
    clipPath = `inset(0 0 0 ${(1 - maskProgress) * 100}%)`;
  } else if (isCurrentProject && isNextProject !== undefined) {
    // Current project: gets hidden from right to left (inverse of next project reveal)
    clipPath = `inset(0 ${maskProgress * 100}% 0 0)`;
  }

  return (
    <div
      className="absolute inset-0 w-full h-full cursor-pointer"
      style={{
        opacity: opacity,
        clipPath: clipPath,
        // Blur effect during mask transition
        filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
      }}
      onClick={handleProjectClick}
    >
      {/* Full background image */}
      <div className="w-full h-full rounded-3xl overflow-hidden">
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-primary/20 flex items-center justify-center">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectContent;
