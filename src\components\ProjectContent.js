"use client";

import { useModal } from '../contexts/ModalContext';

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  const { openModal } = useModal();

  const handleProjectClick = () => {
    openModal('project-details', project);
  };
  return (
    <div
      className="absolute inset-0 w-full h-full cursor-pointer"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
      }}
      onClick={handleProjectClick}
    >
      {/* Full background image with solid backing and radial blur */}
      <div className="w-full h-full rounded-3xl overflow-hidden relative">
        {/* Solid background layer - uses primary theme color */}
        <div className="absolute inset-0 w-full h-full bg-primary"></div>

        {/* Sharp image layer (always visible) */}
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover relative z-10"
          />
        ) : (
          <div className="w-full h-full bg-primary/20 flex items-center justify-center relative z-10">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
          </div>
        )}

        {/* Blurred overlay layer - only visible during transition with radial mask */}
        {blurAmount > 0 && (
          <div className="absolute inset-0 w-full h-full z-20">
            {project.image ? (
              <img
                src={project.image}
                alt={project.title}
                className="w-full h-full object-cover"
                style={{
                  filter: `blur(${blurAmount}px)`,
                  // Radial mask: sharp at edges, visible in center
                  maskImage: 'radial-gradient(circle at center, black 30%, transparent 70%)',
                  WebkitMaskImage: 'radial-gradient(circle at center, black 30%, transparent 70%)'
                }}
              />
            ) : (
              <div
                className="w-full h-full bg-primary/20 flex items-center justify-center"
                style={{
                  filter: `blur(${blurAmount}px)`,
                  // Radial mask: sharp at edges, visible in center
                  maskImage: 'radial-gradient(circle at center, black 30%, transparent 70%)',
                  WebkitMaskImage: 'radial-gradient(circle at center, black 30%, transparent 70%)'
                }}
              >
                <span className="text-secondary text-lg font-medium">{project.title}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectContent;
